/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.dynamic.mcp.transport;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

/**
 * Streamable HTTP Client Transport for MCP protocol.
 * <p>
 * This transport implements a streamable HTTP connection using WebClient and reactive
 * streams. It supports bidirectional communication by maintaining separate input and
 * output message flows.
 * </p>
 */
public class StreamableHttpClientTransport implements McpClientTransport {

	private static final Logger logger = LoggerFactory.getLogger(StreamableHttpClientTransport.class);

	private final WebClient webClient;

	private final ObjectMapper objectMapper;

	// Assume fullUrl is already the complete URL in configuration file
	// No longer concatenate baseUrl/streamEndpoint/query in code, only use fullUrl
	// Please comment or delete other related concatenation logic
	private final String fullUrl;

	private final AtomicBoolean connected = new AtomicBoolean(false);

	private volatile Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>> requestHandler;

	// Stream for bidirectional communication
	private final Sinks.Many<String> outgoingMessages = Sinks.many().multicast().onBackpressureBuffer();

	private final Sinks.Many<String> incomingMessages = Sinks.many().multicast().onBackpressureBuffer();

	// For tracking request-response mapping
	private final Map<String, Sinks.One<String>> pendingRequests = new ConcurrentHashMap<>();

	// Add Session ID support
	private volatile String sessionId = null;

	private final Object sessionIdLock = new Object();

	/**
	 * Constructor for StreamableHttpClientTransport.
	 * @param webClientBuilder WebClient builder with base URL configured
	 * @param objectMapper ObjectMapper for JSON serialization/deserialization
	 * @param streamEndpoint Endpoint path for streaming (e.g., "/stream", "/streamable")
	 */
	public StreamableHttpClientTransport(WebClient.Builder webClientBuilder, ObjectMapper objectMapper,
			String streamEndpoint) {
		logger.info("=== Starting StreamableHttpClientTransport initialization ===");
		logger.info("=== Passed streamEndpoint: {} ===", streamEndpoint);

		this.webClient = webClientBuilder.defaultHeader("Accept", "application/json, text/event-stream").build();
		logger.info("=== WebClient built, default Accept header: application/json, text/event-stream ===");

		this.objectMapper = objectMapper;
		logger.info("=== ObjectMapper configured ===");

		this.fullUrl = streamEndpoint;
		logger.info("=== Complete URL configured: {} ===", this.fullUrl);

		logger.info("=== StreamableHttpClientTransport initialization completed ===");
		logger.info("=== Final configuration: fullUrl={} ===", this.fullUrl);
	}

	/**
	 * Constructor with default stream endpoint.
	 * @param webClientBuilder WebClient builder with base URL configured
	 * @param objectMapper ObjectMapper for JSON serialization/deserialization
	 */
	public StreamableHttpClientTransport(WebClient.Builder webClientBuilder, ObjectMapper objectMapper) {
		this(webClientBuilder, objectMapper, "/stream");
	}

	@Override
	public Mono<Void> connect(Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>> requestHandler) {
		logger.info("=== Starting StreamableHttpClientTransport connection ===");
		logger.info("=== Target URL: {} ===", fullUrl);
		logger.info("=== Current connection status: {} ===", connected.get() ? "Connected" : "Disconnected");

		if (connected.get()) {
			logger.error("=== Transport already connected, cannot connect again ===");
			return Mono.error(new IllegalStateException("Transport is already connected"));
		}

		logger.info("=== Setting request handler ===");
		this.requestHandler = requestHandler;

		logger.info("=== Starting output message processing loop ===");
		// Start output message processing loop
		outgoingMessages.asFlux()
			.doOnNext(message -> logger.info("=== Message received from output stream: {} ===", message))
			.flatMap(this::sendHttpRequest)
			.doOnNext(response -> {
				logger.info("=== HTTP response received: {} ===", response);
				// Push response to input stream
				incomingMessages.tryEmitNext(response);
				logger.info("=== Response pushed to input stream ===");
			})
			.doOnError(error -> {
				logger.error("=== Output stream processing error ===");
				logger.error("=== Error type: {} ===", error.getClass().getSimpleName());
				logger.error("=== Error message: {} ===", error.getMessage());
				logger.error("=== Error stack trace: ===", error);
			})
			.subscribe();

		logger.info("=== Starting input message processing loop ===");
		// Start input message processing loop
		incomingMessages.asFlux()
			.doOnNext(message -> logger.info("=== Message received from input stream: {} ===", message))
			.doOnNext(this::handleIncomingMessage)
			.doOnError(error -> {
				logger.error("=== Input stream processing error ===");
				logger.error("=== Error type: {} ===", error.getClass().getSimpleName());
				logger.error("=== Error message: {} ===", error.getMessage());
				logger.error("=== Error stack trace: ===", error);
			})
			.subscribe();

		connected.set(true);
		logger.info("=== StreamableHttpClientTransport connection successful ===");
		logger.info("=== Connection status set to: {} ===", connected.get());
		return Mono.empty();
	}

	@Override
	public Mono<Void> sendMessage(McpSchema.JSONRPCMessage message) {
		logger.info("=== Starting to send message ===");
		logger.info("=== Message type: {} ===", message.getClass().getSimpleName());
		logger.info("=== Message content: {} ===", message);

		if (!connected.get()) {
			logger.warn("=== Not connected, message will not be sent ===");
			return Mono.empty();
		}

		try {
			// Reflection compatibility for all JSON-RPC message types
			Map<String, Object> jsonRpc = new HashMap<>();
			jsonRpc.put("jsonrpc", "2.0");
			final String[] messageIdHolder = { null };

			logger.info("=== Starting to serialize message fields ===");
			for (String field : new String[] { "id", "method", "params", "result", "error" }) {
				try {
					Method m = message.getClass().getMethod(field);
					Object v = m.invoke(message);
					if (v != null) {
						jsonRpc.put(field, v);
						logger.info("=== Field {}: {} ===", field, v);
						if ("id".equals(field)) {
							messageIdHolder[0] = String.valueOf(v);
							logger.info("=== Message ID: {} ===", messageIdHolder[0]);
						}
					}
					else {
						logger.debug("=== Field {}: null ===", field);
					}
				}
				catch (NoSuchMethodException ignore) {
					logger.debug("=== Field {} does not exist ===", field);
				}
			}

			String jsonMessage = objectMapper.writeValueAsString(jsonRpc);
			logger.info("=== Serialized JSON message: {} ===", jsonMessage);
			logger.info("=== JSON message length: {} bytes ===", jsonMessage.getBytes().length);

			// If it's a request message (with ID), create response waiter
			Mono<Void> result = Mono.empty();
			final String messageId = messageIdHolder[0];
			if (messageId != null) {
				logger.info("=== Creating request response waiter, message ID: {} ===", messageId);
				Sinks.One<String> responseSink = Sinks.one();
				pendingRequests.put(messageId, responseSink);
				logger.info("=== Current number of pending requests: {} ===", pendingRequests.size());

				// Wait for response (but don't block sendMessage return)
				result = responseSink.asMono()
					.doOnNext(response -> logger.info("=== Received response for request {}: {} ===", messageId,
							response))
					.then();
			}
			else {
				logger.info("=== This is a notification message (no ID), no need to wait for response ===");
			}

			// Send message to output stream
			logger.info("=== Pushing message to output stream ===");
			outgoingMessages.tryEmitNext(jsonMessage);
			logger.info("=== Message pushed to output stream ===");

			return result;
		}
		catch (Exception e) {
			logger.error("=== Failed to send message ===", e);
			logger.error("=== Failed message content: {} ===", message);
			return Mono.error(e);
		}
	}

	private Mono<String> sendHttpRequest(String jsonMessage) {
		logger.info("=== Starting HTTP request ===");
		logger.info("=== Request URL: {} ===", fullUrl);
		logger.info("=== Request method: POST ===");
		logger.info("=== Current Session ID: {} ===", sessionId);

		// Build request headers
		String acceptHeader = "application/json, text/event-stream";
		logger.info("=== Request headers: Content-Type=application/json, Accept={} ===", acceptHeader);
		logger.info("=== Request body: {} ===", jsonMessage);
		logger.info("=== Request body length: {} bytes ===", jsonMessage.getBytes().length);

		// Build WebClient request
		WebClient.RequestBodySpec requestSpec = webClient.post()
			.uri(fullUrl)
			.contentType(MediaType.APPLICATION_JSON)
			.header("Accept", acceptHeader);

		// If there is Session ID, add to request header
		if (sessionId != null) {
			requestSpec.header("MCP-Session-ID", sessionId);
			logger.info("=== Adding Session ID to request header: {} ===", sessionId);
		}
		else {
			logger.info("=== No Session ID currently, skipping Session ID header ===");
		}

		return requestSpec.bodyValue(jsonMessage).exchangeToMono(clientResponse -> {
			logger.info("=== Received HTTP response ===");
			logger.info("=== Response status: {} ===", clientResponse.statusCode());
			logger.info("=== Response headers: {} ===", clientResponse.headers().asHttpHeaders());

			// Extract Session ID from response headers
			extractSessionIdFromHeaders(clientResponse.headers().asHttpHeaders());

			if (clientResponse.statusCode().is2xxSuccessful()) {
				logger.info("=== HTTP request successful ===");
				return clientResponse.bodyToMono(String.class).doOnNext(response -> {
					logger.info("=== Response body: {} ===", response);
					logger.info("=== Response body length: {} bytes ===",
							response != null ? response.getBytes().length : 0);
				});
			}
			else {
				logger.error("=== HTTP request failed, status code: {} ===", clientResponse.statusCode());
				return clientResponse.bodyToMono(String.class).flatMap(errorBody -> {
					logger.error("=== Error response body: {} ===", errorBody);
					return Mono.error(new org.springframework.web.reactive.function.client.WebClientResponseException(
							clientResponse.statusCode().value(), clientResponse.statusCode().toString(),
							clientResponse.headers().asHttpHeaders(), errorBody.getBytes(), null));
				});
			}
		}).timeout(Duration.ofSeconds(30)).doOnError(error -> {
			logger.error("=== HTTP request failed ===");
			logger.error("=== Error type: {} ===", error.getClass().getSimpleName());
			logger.error("=== Error message: {} ===", error.getMessage());

			if (error instanceof org.springframework.web.reactive.function.client.WebClientResponseException) {
				org.springframework.web.reactive.function.client.WebClientResponseException wcre = (org.springframework.web.reactive.function.client.WebClientResponseException) error;
				logger.error("=== HTTP status code: {} ===", wcre.getStatusCode());
				logger.error("=== HTTP status text: {} ===", wcre.getStatusText());
				logger.error("=== Response headers: {} ===", wcre.getHeaders());
				logger.error("=== Response body: {} ===", wcre.getResponseBodyAsString());
			}
		});
	}

	private void handleIncomingMessage(String responseJson) {
		try {
			logger.info("=== Starting to process input message ===");
			logger.info("=== Raw response content: {} ===", responseJson);
			logger.info("=== Raw response length: {} bytes ===",
					responseJson != null ? responseJson.getBytes().length : 0);

			// Auto-detect format and parse
			String jsonContent = parseResponseFormat(responseJson);
			if (jsonContent == null) {
				logger.error("=== Unable to parse response format, raw content: {} ===", responseJson);
				return;
			}

			logger.info("=== Parsed JSON content: {} ===", jsonContent);
			logger.info("=== Parsed JSON length: {} bytes ===", jsonContent.getBytes().length);

			// Parse response
			Map<String, Object> data = objectMapper.readValue(jsonContent, Map.class);
			String responseId = (String) data.get("id");
			String method = (String) data.get("method");
			String jsonrpc = (String) data.get("jsonrpc");

			logger.info("=== Parsed response fields ===");
			logger.info("=== jsonrpc: {} ===", jsonrpc);
			logger.info("=== method: {} ===", method);
			logger.info("=== id: {} ===", responseId);
			logger.info("=== Complete parsed data: {} ===", data);

			// Use officially recommended deserialization method
			McpSchema.JSONRPCMessage messageObj = null;
			try {
				messageObj = McpSchema.deserializeJsonRpcMessage(objectMapper, jsonContent);
				logger.info("=== Successfully deserialized to JSONRPCMessage ===");
			}
			catch (Exception e) {
				logger.warn("=== Failed to deserialize to JSONRPCMessage: {} ===", e.getMessage());
				logger.warn("=== JSON content that failed deserialization: {} ===", jsonContent);
			}

			if (responseId != null && pendingRequests.containsKey(responseId)) {
				// This is a response to some request
				logger.info("=== Found response waiter for request {} ===", responseId);
				Sinks.One<String> responseSink = pendingRequests.remove(responseId);
				responseSink.tryEmitValue(jsonContent);
				logger.info("=== Received response for request {}: {} ===", responseId, jsonContent);

				if (requestHandler != null && messageObj != null) {
					logger.info("=== Processing response through requestHandler (type-safe mode) ===");
					try {
						requestHandler.apply(Mono.just(messageObj))
							.subscribe(result -> logger.info("=== requestHandler processing completed: {} ===", result),
									error -> logger.error("=== requestHandler processing error: {} ===",
											String.valueOf(error), error),
									() -> logger.info("=== requestHandler processing stream completed ==="));
					}
					catch (Exception e) {
						logger.error("=== requestHandler call failed: {} ===", e.getMessage(), e);
					}
				}
				else if (requestHandler != null) {
					logger.warn("=== Failed to deserialize to JSONRPCMessage, skipping requestHandler processing ===");
				}
			}
			else {
				logger.info("=== This is server-initiated message or unknown response: {} ===", jsonContent);
				logger.info("=== Currently pending request IDs: {} ===", pendingRequests.keySet());
				// For server-initiated messages, also process through requestHandler
				if (requestHandler != null && messageObj != null) {
					try {
						requestHandler.apply(Mono.just(messageObj)).subscribe();
					}
					catch (Exception e) {
						logger.error("=== Failed to process server message: {} ===", e.getMessage(), e);
					}
				}
				else if (requestHandler != null) {
					logger.warn("=== Failed to deserialize to JSONRPCMessage, skipping requestHandler processing ===");
				}
			}

			logger.info("=== Input message processing completed ===");
		}
		catch (Exception e) {
			logger.error("=== Failed to process input message: {} ===", e.getMessage(), e);
			logger.error("=== Failed raw response: {} ===", responseJson);
		}
	}

	/**
	 * Parse response format, supports SSE and JSON formats
	 * @param rawResponse Raw response content
	 * @return Parsed JSON content, return null if parsing fails
	 */
	private String parseResponseFormat(String rawResponse) {
		logger.info("=== 开始解析响应格式 ===");
		logger.info("=== 原始响应: {} ===", rawResponse);

		if (rawResponse == null || rawResponse.trim().isEmpty()) {
			logger.warn("=== 原始响应为空或null ===");
			return null;
		}

		String trimmedResponse = rawResponse.trim();
		logger.info("=== 去除首尾空格后的响应: {} ===", trimmedResponse);
		logger.info("=== 响应长度: {} 字符 ===", trimmedResponse.length());

		// 检测是否为SSE格式
		boolean isSse = isSseFormat(trimmedResponse);
		logger.info("=== 格式检测结果: {} ===", isSse ? "SSE格式" : "JSON格式");

		if (isSse) {
			logger.info("=== 检测到SSE格式，开始解析 ===");
			String result = parseSseFormat(trimmedResponse);
			logger.info("=== SSE解析结果: {} ===", result);
			return result;
		}
		else {
			logger.info("=== 检测到JSON格式，直接使用 ===");
			String result = parseJsonFormat(trimmedResponse);
			logger.info("=== JSON解析结果: {} ===", result);
			return result;
		}
	}

	/**
	 * 检测是否为SSE格式
	 * @param response 响应内容
	 * @return true if SSE format, false otherwise
	 */
	private boolean isSseFormat(String response) {
		logger.debug("=== 开始检测SSE格式 ===");
		logger.debug("=== 检测内容: {} ===", response);

		// 标准化换行符，处理\r\n和\n的情况
		String normalizedResponse = response.replace("\r\n", "\n");

		// SSE格式特征：包含event:和data:字段，或者以event:开头
		boolean startsWithEvent = normalizedResponse.startsWith("event:");
		boolean containsEvent = normalizedResponse.contains("event:");
		boolean containsData = normalizedResponse.contains("data:");

		logger.debug("=== SSE格式检测结果 ===");
		logger.debug("=== 以event:开头: {} ===", startsWithEvent);
		logger.debug("=== 包含event:: {} ===", containsEvent);
		logger.debug("=== 包含data:: {} ===", containsData);

		boolean isSse = startsWithEvent || (containsEvent && containsData);
		logger.debug("=== 最终SSE格式判断: {} ===", isSse);

		return isSse;
	}

	/**
	 * 解析SSE格式
	 * @param sseResponse SSE格式的响应
	 * @return 提取的JSON内容
	 */
	private String parseSseFormat(String sseResponse) {
		logger.info("=== 开始解析SSE格式 ===");
		logger.info("=== SSE原始内容: {} ===", sseResponse);

		try {
			// 标准化换行符，处理\r\n和\n的情况
			String normalizedResponse = sseResponse.replace("\r\n", "\n");

			// 按行分割
			String[] lines = normalizedResponse.split("\n");
			logger.info("=== SSE行数: {} ===", lines.length);

			StringBuilder jsonContent = new StringBuilder();
			boolean inDataSection = false;

			for (int i = 0; i < lines.length; i++) {
				String line = lines[i].trim();
				logger.debug("=== 处理第{}行: {} ===", i + 1, line);

				if (line.isEmpty()) {
					logger.debug("=== 跳过空行 ===");
					continue;
				}

				if (line.startsWith("data:")) {
					// 提取data:后面的内容
					String data = line.substring(5).trim();
					logger.info("=== 找到data字段: {} ===", data);
					if (!data.isEmpty()) {
						jsonContent.append(data);
						inDataSection = true;
						logger.info("=== 已添加到JSON内容 ===");
					}
				}
				else if (inDataSection && !line.startsWith("event:") && !line.startsWith("id:")
						&& !line.startsWith("retry:")) {
					// 如果已经在data部分，且不是SSE控制字段，则可能是多行JSON的一部分
					logger.info("=== 添加多行JSON内容: {} ===", line);
					jsonContent.append(line);
				}
				else {
					logger.debug("=== 跳过SSE控制字段: {} ===", line);
				}
			}

			String result = jsonContent.toString().trim();
			logger.info("=== SSE解析完成，结果: {} ===", result);

			if (result.isEmpty()) {
				logger.warn("=== SSE格式解析失败，未找到data内容 ===");
				return null;
			}

			logger.info("=== SSE格式解析成功 ===");
			return result;

		}
		catch (Exception e) {
			logger.error("=== SSE格式解析异常: {} ===", e.getMessage(), e);
			logger.error("=== 解析失败的SSE内容: {} ===", sseResponse);
			return null;
		}
	}

	/**
	 * 解析JSON格式（直接返回，或进行基本验证）
	 * @param jsonResponse JSON格式的响应
	 * @return JSON内容
	 */
	private String parseJsonFormat(String jsonResponse) {
		logger.info("=== 开始解析JSON格式 ===");
		logger.info("=== JSON原始内容: {} ===", jsonResponse);

		try {
			// 检查是否包含SSE格式的前缀，如果是则先尝试解析SSE格式
			if (jsonResponse.contains("event:") || jsonResponse.contains("data:")) {
				logger.warn("=== 检测到SSE格式前缀，但被误判为JSON格式，尝试解析SSE ===");
				String sseResult = parseSseFormat(jsonResponse);
				if (sseResult != null) {
					logger.info("=== SSE解析成功，返回结果: {} ===", sseResult);
					return sseResult;
				}
				else {
					logger.error("=== SSE解析失败，原始内容可能格式错误 ===");
					return null;
				}
			}
			// 尝试解析JSON以验证格式
			Object parsed = objectMapper.readValue(jsonResponse, Object.class);
			logger.info("=== JSON格式验证成功，解析结果类型: {} ===", parsed.getClass().getSimpleName());
			return jsonResponse;
		}
		catch (Exception e) {
			logger.error("=== JSON格式验证失败: {} ===", e.getMessage(), e);
			logger.error("=== 验证失败的JSON内容: {} ===", jsonResponse);
			return null;
		}
	}

	/**
	 * 创建响应消息实例
	 */
	private McpSchema.JSONRPCMessage createResponseMessage(Map<String, Object> data) {
		try {
			logger.info("=== 尝试创建响应消息实例 ===");

			// 转换为JSON字符串
			String json = objectMapper.writeValueAsString(data);
			logger.info("=== 转换为JSON字符串: {} ===", json);

			// 尝试使用 unmarshalFrom 方法
			try {
				McpSchema.JSONRPCMessage message = objectMapper.readValue(json, McpSchema.JSONRPCMessage.class);
				logger.info("=== 通过 readValue 创建成功 ===");
				return message;
			}
			catch (Exception e) {
				logger.warn("=== readValue 失败: {} ===", e.getMessage());
			}

			// 尝试使用 unmarshalFrom 方法
			try {
				TypeReference<McpSchema.JSONRPCMessage> typeRef = new TypeReference<McpSchema.JSONRPCMessage>() {
				};
				McpSchema.JSONRPCMessage message = unmarshalFrom(data, typeRef);
				logger.info("=== 通过 unmarshalFrom 创建成功 ===");
				return message;
			}
			catch (Exception e) {
				logger.warn("=== unmarshalFrom 失败: {} ===", e.getMessage());
			}

			logger.warn("=== 所有创建方法都失败，返回 null ===");
			return null;
		}
		catch (Exception e) {
			logger.error("Failed to unmarshal data", e);
			logger.warn("=== unmarshalFrom 失败: {} ===", e.getMessage());
			logger.warn("=== 所有创建方法都失败，返回 null ===");
			return null;
		}
	}

	@Override
	public <T> T unmarshalFrom(Object data, TypeReference<T> typeReference) {
		try {
			String json = objectMapper.writeValueAsString(data);
			return objectMapper.readValue(json, typeReference);
		}
		catch (Exception e) {
			logger.error("Failed to unmarshal data", e);
			throw new RuntimeException("Failed to unmarshal data", e);
		}
	}

	@Override
	public void close() {
		logger.info("Closing StreamableHttpClientTransport");
		connected.set(false);
		outgoingMessages.tryEmitComplete();
		incomingMessages.tryEmitComplete();
	}

	@Override
	public Mono<Void> closeGracefully() {
		logger.info("Gracefully closing StreamableHttpClientTransport");
		return Mono.fromRunnable(this::close);
	}

	/**
	 * 从响应头中提取Session ID
	 * @param headers HTTP响应头
	 */
	private void extractSessionIdFromHeaders(org.springframework.http.HttpHeaders headers) {
		logger.info("=== 开始从响应头提取Session ID ===");
		logger.info("=== 响应头: {} ===", headers);

		// 尝试从不同的头字段中获取Session ID
		String newSessionId = headers.getFirst("mcp-session-id");
		if (newSessionId == null) {
			newSessionId = headers.getFirst("MCP-Session-ID");
		}
		if (newSessionId == null) {
			newSessionId = headers.getFirst("session-id");
		}
		if (newSessionId == null) {
			newSessionId = headers.getFirst("Session-ID");
		}

		if (newSessionId != null && !newSessionId.trim().isEmpty()) {
			synchronized (sessionIdLock) {
				this.sessionId = newSessionId.trim();
				logger.info("=== 成功提取Session ID: {} ===", this.sessionId);
			}
		}
		else {
			logger.warn("=== 响应头中未找到Session ID ===");
		}
	}

	/**
	 * 获取当前Session ID
	 * @return 当前Session ID，如果未设置则返回null
	 */
	public String getSessionId() {
		return sessionId;
	}

	/**
	 * 设置Session ID
	 * @param sessionId 要设置的Session ID
	 */
	public void setSessionId(String sessionId) {
		synchronized (sessionIdLock) {
			this.sessionId = sessionId;
			logger.info("=== 手动设置Session ID: {} ===", this.sessionId);
		}
	}

}
