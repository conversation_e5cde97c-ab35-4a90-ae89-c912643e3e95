#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from pathlib import Path

def find_chinese_content():
    """Find all Java files containing Chinese characters"""
    base_path = Path('spring-ai-alibaba-jmanus/src/main/java')
    chinese_files = []
    
    for java_file in base_path.rglob('*.java'):
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if file contains Chinese characters (excluding copyright)
            lines = content.split('\n')
            chinese_lines = []
            
            for line_num, line in enumerate(lines, 1):
                if re.search(r'[\u4e00-\u9fa5]', line):
                    # Skip copyright and license lines
                    if not any(keyword in line.lower() for keyword in ['copyright', 'licensed', 'apache', 'limitations', 'warranties', 'conditions']):
                        chinese_lines.append((line_num, line.strip()))
            
            if chinese_lines:
                chinese_files.append({
                    'file': str(java_file),
                    'lines': chinese_lines
                })
                
        except Exception as e:
            print(f"Error reading {java_file}: {e}")
    
    return chinese_files

def categorize_content(line):
    """Categorize the type of Chinese content"""
    line_lower = line.lower()
    
    # JavaDoc
    if line.strip().startswith('/**') or line.strip().startswith('*') and not line.strip().startswith('*/'):
        return 'javadoc'
    
    # Single line comments
    if line.strip().startswith('//'):
        return 'comment'
    
    # Log statements
    if any(keyword in line for keyword in ['log.', 'logger.', 'Logger.', 'System.out.print', 'System.err.print']):
        return 'log'
    
    # Exception/Error messages
    if any(keyword in line for keyword in ['throw new', 'Exception', 'Error', 'RuntimeException', 'IllegalArgumentException']):
        return 'error'
    
    # Prompt related (string literals that might be prompts)
    if '"' in line and any(keyword in line for keyword in ['prompt', 'Prompt', 'template', 'Template']):
        return 'prompt'
    
    # PromptEnum
    if 'PromptEnum' in line:
        return 'prompt_enum'
    
    # ManusProperties
    if 'ManusProperties' in line or '@ConfigurationProperties' in line:
        return 'manus_properties'
    
    # FormInputTool
    if 'FormInputTool' in line:
        return 'form_input_tool'
    
    # String literals in general
    if '"' in line:
        return 'string_literal'
    
    # Other types
    return 'other'

def main():
    print("Searching for Chinese content in Java files...")
    chinese_files = find_chinese_content()
    
    if not chinese_files:
        print("No Chinese content found.")
        return
    
    print(f"\nFound {len(chinese_files)} files with Chinese content:\n")
    
    categories = {
        'javadoc': [],
        'comment': [],
        'log': [],
        'error': [],
        'prompt': [],
        'prompt_enum': [],
        'manus_properties': [],
        'form_input_tool': [],
        'string_literal': [],
        'other': []
    }
    
    for file_info in chinese_files:
        file_path = file_info['file']
        print(f"=== {file_path} ===")
        
        for line_num, line in file_info['lines']:
            category = categorize_content(line)
            categories[category].append({
                'file': file_path,
                'line': line_num,
                'content': line
            })
            print(f"  Line {line_num} [{category}]: {line}")
        print()
    
    # Summary by category
    print("\n=== SUMMARY BY CATEGORY ===")
    for category, items in categories.items():
        if items:
            print(f"\n{category.upper()} ({len(items)} items):")
            for item in items[:5]:  # Show first 5 items
                print(f"  {item['file']}:{item['line']} - {item['content'][:80]}...")
            if len(items) > 5:
                print(f"  ... and {len(items) - 5} more items")

if __name__ == "__main__":
    main()
