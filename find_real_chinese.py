#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from pathlib import Path

def find_files_with_real_chinese():
    """Find Java files with actual Chinese content (not just copyright)"""
    base_path = Path('spring-ai-alibaba-jmanus/src/main/java')
    files_with_chinese = []
    
    for java_file in base_path.rglob('*.java'):
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            chinese_lines = []
            for line_num, line in enumerate(lines, 1):
                if re.search(r'[\u4e00-\u9fa5]', line):
                    # Skip copyright and license lines
                    if not any(keyword in line.lower() for keyword in [
                        'copyright', 'licensed', 'apache', 'limitations', 
                        'warranties', 'conditions', 'you may not use this file',
                        'you may obtain a copy', 'unless required by applicable law',
                        'see the license for the specific language governing permissions'
                    ]):
                        chinese_lines.append((line_num, line.strip()))
            
            if chinese_lines:
                files_with_chinese.append({
                    'file': str(java_file),
                    'lines': chinese_lines
                })
                
        except Exception as e:
            print(f"Error reading {java_file}: {e}")
    
    return files_with_chinese

def main():
    print("Finding Java files with actual Chinese content...")
    chinese_files = find_files_with_real_chinese()
    
    if not chinese_files:
        print("No files with actual Chinese content found.")
        return
    
    print(f"\nFound {len(chinese_files)} files with actual Chinese content:\n")
    
    for file_info in chinese_files:
        file_path = file_info['file']
        print(f"=== {file_path} ===")
        
        for line_num, line in file_info['lines'][:5]:  # Show first 5 lines
            print(f"  Line {line_num}: {line}")
        
        if len(file_info['lines']) > 5:
            print(f"  ... and {len(file_info['lines']) - 5} more lines")
        print()

if __name__ == "__main__":
    main()
